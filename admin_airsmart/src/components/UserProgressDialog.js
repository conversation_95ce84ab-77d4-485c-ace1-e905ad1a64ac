import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  CircularProgress,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Divider,
  Card,
  CardContent,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  School as SchoolIcon,
  PlayArrow as PlayArrowIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';

const UserProgressDialog = ({ 
  open, 
  onClose, 
  user, 
  progressDetail, 
  loading, 
  onAdjustProgress, 
  onResetProgress 
}) => {
  const [editingModule, setEditingModule] = useState(null);
  const [selectedStep, setSelectedStep] = useState('');
  const [savingProgress, setSavingProgress] = useState(false);
  const [resettingModule, setResettingModule] = useState(null);
  const [resettingAll, setResettingAll] = useState(false);
  const [expandedTopics, setExpandedTopics] = useState({});


  // Helper function to format Firestore timestamp
  const formatFirestoreDate = (timestamp) => {
    if (!timestamp) return null;

    try {
      // Handle Firestore timestamp format with _seconds
      if (timestamp._seconds) {
        return new Date(timestamp._seconds * 1000).toLocaleDateString();
      }
      // Handle Firestore timestamp format with seconds
      if (timestamp.seconds) {
        return new Date(timestamp.seconds * 1000).toLocaleDateString();
      }
      // Handle regular date
      if (timestamp.toDate) {
        return timestamp.toDate().toLocaleDateString();
      }
      // Handle string date
      return new Date(timestamp).toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  const handleEditModule = (courseId, moduleId, currentStepId) => {
    setEditingModule({ courseId, moduleId });
    setSelectedStep(currentStepId || '');
  };

  const toggleTopics = (moduleId) => {
    setExpandedTopics(prev => ({
      ...prev,
      [moduleId]: !prev[moduleId]
    }));
  };

  const handleSaveProgress = async () => {
    if (!editingModule || !user) return;

    try {
      setSavingProgress(true);

      const adjustData = {
        userId: user.uid,
        moduleId: editingModule.moduleId,
        currentStepId: selectedStep === 'not-started' ? null : selectedStep,
        completed: selectedStep === 'completed',
        jumpToPoint: true // Always jump to point when editing
      };

      await onAdjustProgress(adjustData);
      setEditingModule(null);
      setSelectedStep('');
    } catch (error) {
      console.error('Error saving progress:', error);
    } finally {
      setSavingProgress(false);
    }
  };

  const handleResetModule = async (moduleId) => {
    if (!user) return;

    if (window.confirm('Are you sure you want to reset progress for this module?')) {
      try {
        setResettingModule(moduleId);
        await onResetProgress({
          userId: user.uid,
          moduleId: moduleId
        });
      } catch (error) {
        console.error('Error resetting module:', error);
      } finally {
        setResettingModule(null);
      }
    }
  };

  const handleResetAllProgress = async () => {
    if (!user) return;

    if (window.confirm('Are you sure you want to reset ALL progress for this user?')) {
      try {
        setResettingAll(true);
        await onResetProgress({
          userId: user.uid
        });
      } catch (error) {
        console.error('Error resetting all progress:', error);
      } finally {
        setResettingAll(false);
      }
    }
  };

  const getStepIcon = (step, currentStepId) => {
    if (step.stepId === currentStepId || step.isCurrent) {
      return <PlayArrowIcon color="primary" />;
    } else {
      return <RadioButtonUncheckedIcon color="disabled" />;
    }
  };

  const getModuleStatusChip = (module) => {
    if (module.completed) {
      // if (module.autoCompleted) {
      //   return <Chip label="Auto-Completed" color="info" size="small" />;
      // }
      return <Chip label="Completed" color="success" size="small" />;
    } else if (module.currentStepId && module.currentStepId !== 'completed') {
      return <Chip label="In Progress" color="primary" size="small" />;
    } else {
      return <Chip label="Not Started" color="default" size="small" />;
    }
  };

  if (!user) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      slotProps={{
        paper: {
          sx: { height: '90vh' }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6">
              Edit Progress: {user.displayName || user.email}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {user.email}
            </Typography>
          </Box>
          <Button
            variant="outlined"
            color="error"
            size="small"
            startIcon={resettingAll ? <CircularProgress size={16} /> : <DeleteIcon />}
            onClick={handleResetAllProgress}
            disabled={resettingAll}
          >
            {resettingAll ? 'Resetting...' : 'Reset All'}
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <CircularProgress />
          </Box>
        ) : progressDetail ? (
          <Box>
            {/* Overall Progress Summary */}
            <Card sx={{ m: 2, mb: 1 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Overall Progress
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={progressDetail.overallProgress} 
                    sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    {progressDetail.overallProgress}%
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {progressDetail.completedModules} of {progressDetail.totalModules} modules completed
                </Typography>
              </CardContent>
            </Card>

            {/* Courses and Modules */}
            <Box sx={{ px: 2 }}>
              {progressDetail.courses?.map((course) => (
                <Accordion key={course.courseId} >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <SchoolIcon color="primary" />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {course.courseName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {course.completedModules} of {course.totalModules} modules completed
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={course.totalModules > 0 ? (course.completedModules / course.totalModules) * 100 : 0}
                        sx={{ width: 100, height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {course.modules?.map((module) => (
                        <ListItem key={module.moduleId} sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 1 }}>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="subtitle2">
                                    {module.moduleName}
                                  </Typography>
                                  {getModuleStatusChip(module)}
                                </Box>
                              }
                              secondary={
                                <Typography variant="body2" color="text.secondary">
                                  {module.totalSteps} topics
                                  {module.currentStepId && ` • Current: ${module.currentStepId}`}
                                  {module.lastUpdated && ` • Updated: ${formatFirestoreDate(module.lastUpdated)}`}
                                </Typography>
                              }
                            />
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {module.steps && module.steps.length > 0 && (
                                <IconButton
                                  size="small"
                                  onClick={() => toggleTopics(module.moduleId)}
                                  color="default"
                                  title={expandedTopics[module.moduleId] ? "Hide topics" : "Show topics"}
                                >
                                  {expandedTopics[module.moduleId] ?
                                    <VisibilityOffIcon fontSize="small" /> :
                                    <VisibilityIcon fontSize="small" />
                                  }
                                </IconButton>
                              )}
                              <IconButton
                                size="small"
                                onClick={() => handleEditModule(course.courseId, module.moduleId, module.currentStepId)}
                                color="primary"
                                disabled={savingProgress || resettingModule === module.moduleId}
                                title="Edit progress"
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>

                          {/* Edit Module Progress */}
                          {editingModule?.moduleId === module.moduleId && (
                            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Edit Progress for: {module.moduleName}
                              </Typography>
                              <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                                <InputLabel>Current Topic</InputLabel>
                                <Select
                                  value={selectedStep}
                                  onChange={(e) => setSelectedStep(e.target.value)}
                                  label="Current Topic"
                                  disabled={savingProgress}
                                >
                                  <MenuItem value="not-started">Not Started</MenuItem>

                                  {module.steps?.map((step) => (
                                    <MenuItem key={step.stepId} value={step.stepId}>
                                      {step.title}
                                    </MenuItem>
                                  ))}
                                  <MenuItem value="completed">Completed</MenuItem>
                                </Select>
                              </FormControl>
                              <Alert severity="info" sx={{ mb: 2 }}>
                                Editing progress will automatically jump to the selected point and adjust related modules accordingly.
                              </Alert>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Button
                                  variant="contained"
                                  size="small"
                                  onClick={handleSaveProgress}
                                  disabled={savingProgress}
                                  startIcon={savingProgress ? <CircularProgress size={16} /> : null}
                                >
                                  {savingProgress ? 'Saving...' : 'Save'}
                                </Button>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  onClick={() => {
                                    setEditingModule(null);
                                    setSelectedStep('');
                                  }}
                                  disabled={savingProgress}
                                >
                                  Cancel
                                </Button>
                              </Box>
                            </Box>
                          )}

                          {/* Steps List - Only show when expanded */}
                          {module.steps && module.steps.length > 0 && expandedTopics[module.moduleId] && (
                            <Box sx={{ mt: 1, pl: 2 }}>
                              <Typography variant="caption" color="text.secondary" gutterBottom>
                                Topics ({module.steps.length}):
                              </Typography>
                              <List dense sx={{ pt: 0 }}>
                                {module.steps.map((step) => (
                                  <ListItem key={step.stepId} sx={{ py: 0.5, pl: 1 }}>
                                    <ListItemIcon sx={{ minWidth: 32 }}>
                                      {getStepIcon(step, module.currentStepId)}
                                    </ListItemIcon>
                                    <ListItemText
                                      primary={
                                        <Typography variant="body2">
                                          {step.title}
                                        </Typography>
                                      }
                                    />
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          )}
                          
                          <Divider sx={{ mt: 2 }} />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          </Box>
        ) : (
          <Box sx={{ p: 3 }}>
            <Alert severity="info">
              No progress data found for this user.
            </Alert>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserProgressDialog;
