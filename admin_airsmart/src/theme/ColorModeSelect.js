import * as React from 'react';
import { useColorScheme } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';

export default function ColorModeSelect(props) {
  const { mode, setMode } = useColorScheme();
  if (!mode) {
    return null;
  }
  const handleToggle = () => {
    let newMode;
    if (mode === 'system') {
      // Nếu đang là system, lấy trạng thái hệ điều hành để chuyển đúng
      const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      newMode = systemDark ? 'light' : 'dark';
    } else {
      newMode = mode === 'dark' ? 'light' : 'dark';
    }
    setMode(newMode);
  };

  const Icon = mode === 'dark' ? LightModeIcon : DarkModeIcon;
  const tooltip = mode === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';

  return (
    <Tooltip title={tooltip}>
      <IconButton onClick={handleToggle} {...props}>
        <Icon />
      </IconButton>
    </Tooltip>
  );
}